import { generatePlaceholderImage } from "@/utils/helper";
import { TBlogPost } from "@/types/blog";

export const ENV = {
  PROD: 'production',
  DEV: 'dev',
  LOCAL: 'local',
};

export const ROUTE_FOLDER = [
  'category',
  'tag',
  // 'page',
];

export const QUERY_KEY = {
  BLOGS: 'blogs'
};

export const CLARITY_PROJECT_ID = 'prnrj32taf';

export const CSLANT_ADDRESS = 'Floor 5, CF Tower, 70 Pham Ngoc Thach Street, Ward Vo Thi Sau, District 3, Ho Chi Minh City, Vietnam'
export const CSLANT_ADDRESS_SHORT = '5F, CF Tower, 70 Pham Ngoc Thach St., District 3, HCMC, VN'
export const CSLANT_CONTACT_EMAIL = '<EMAIL>'
export const CSLANT_PHONE_NUMBER = '+84 ***********'
export const CSLANT_PHONE_DISPLAY = '(+84) ***********'

export const SOCIAL_GOOGLE_MAP_URL = 'https://maps.app.goo.gl/D1am7PAkf7F9Dv46A'
export const SOCIAL_LINKEDIN_URL = 'https://www.linkedin.com/company/cslant'
export const SOCIAL_FACEBOOK_URL = 'https://www.facebook.com/cslant'
export const SOCIAL_TWITTER_URL = 'https://x.com/cslant_com'
export const SOCIAL_YOUTUBE_URL = 'https://www.youtube.com/@cslant_com'
export const SOCIAL_GITHUB_URL = 'https://github.com/cslant'

export const POST_LOADING_DATA: TBlogPost = {
  id: Math.random(),
  name: 'Loading...',
  slug: '#',
  author: {
    image: generatePlaceholderImage({ x: 40, y: 40 }),
    role: 'Loading...',
    first_name: 'Loading...',
    last_name: '',
    full_name: 'Loading...'
  },
  image: generatePlaceholderImage({}),
  description: 'Loading...',
  tags: [{
    id: Math.random(),
    name: 'Loading...',
    slug: '#',
    description: ''
  }],
  created_at: '',
  updated_at: '',
  categories: [{
    id: Math.random(),
    name: 'Loading...',
    slug: '#',
    description: 'Loading...'
  }],
  content: "Loading..."
};
