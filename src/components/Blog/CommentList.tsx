"use client";

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { commentApi } from '@/api/blog/comment';
import { TComment } from '@/types/blog/comment';
import CommentForm from './CommentForm';
import { formatDate } from '@/utils/helper';

interface CommentListProps {
  postId: number;
  refreshTrigger?: number;
}

interface CommentItemProps {
  comment: TComment;
  postId: number;
  level: number;
  onCommentAdded: () => void;
}

const CommentItem: React.FC<CommentItemProps> = ({ 
  comment, 
  postId, 
  level, 
  onCommentAdded 
}) => {
  const [showReplyForm, setShowReplyForm] = useState(false);
  const canReply = level < 2; // Chỉ cho phép reply tối đa 2 cấp

  const handleReplyClick = () => {
    if (canReply) {
      setShowReplyForm(!showReplyForm);
    }
  };

  const handleReplyAdded = () => {
    setShowReplyForm(false);
    onCommentAdded();
  };

  const getAvatarUrl = (email: string) => {
    // Sử dụng Gravatar hoặc avatar mặc định
    return comment.author_avatar || `https://www.gravatar.com/avatar/${btoa(email)}?d=identicon&s=95`;
  };

  return (
    <li className="comment">
      <article className="comment-body">
        <footer className="comment-meta">
          <div className="comment-author vcard">
            <Image
              src={getAvatarUrl(comment.author_email)}
              className="avatar"
              alt={comment.author_name}
              width={95}
              height={95}
            />
            <b className="fn">{comment.author_name}</b>
            <span className="says">says:</span>
          </div>

          <div className="comment-metadata">
            {formatDate(comment.created_at)}
          </div>
        </footer>

        <div className="comment-content">
          <p>{comment.content}</p>
        </div>

        {canReply && (
          <div className="reply">
            <Link 
              href="#" 
              className="comment-reply-link"
              onClick={(e) => {
                e.preventDefault();
                handleReplyClick();
              }}
            >
              Reply
            </Link>
          </div>
        )}
      </article>

      {/* Reply Form */}
      {showReplyForm && (
        <div className="reply-form-container">
          <CommentForm
            postId={postId}
            parentId={comment.id}
            onCommentAdded={handleReplyAdded}
            onCancel={() => setShowReplyForm(false)}
            isReply={true}
            placeholder={`Reply to ${comment.author_name}...`}
          />
        </div>
      )}

      {/* Nested Replies */}
      {comment.replies && comment.replies.length > 0 && (
        <ol className="children">
          {comment.replies.map((reply) => (
            <CommentItem
              key={reply.id}
              comment={reply}
              postId={postId}
              level={level + 1}
              onCommentAdded={onCommentAdded}
            />
          ))}
        </ol>
      )}
    </li>
  );
};

const CommentList: React.FC<CommentListProps> = ({ postId, refreshTrigger }) => {
  const [comments, setComments] = useState<TComment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchComments = async () => {
    try {
      setLoading(true);
      const response = await commentApi.getByPost(postId);
      
      if (response.error) {
        setError(response.message || 'Failed to load comments');
      } else {
        setComments(response.data || []);
        setError(null);
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchComments();
  }, [postId, refreshTrigger]);

  const handleCommentAdded = () => {
    fetchComments();
  };

  if (loading) {
    return (
      <div className="comments-area">
        <div className="loading">Loading comments...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="comments-area">
        <div className="error">Error: {error}</div>
      </div>
    );
  }

  return (
    <div className="comments-area">
      <h3 id="comments" className="comments-title">
        {comments.length} {comments.length === 1 ? 'Comment' : 'Comments'}
      </h3>

      {comments.length > 0 && (
        <ol className="comment-list">
          {comments.map((comment) => (
            <CommentItem
              key={comment.id}
              comment={comment}
              postId={postId}
              level={0}
              onCommentAdded={handleCommentAdded}
            />
          ))}
        </ol>
      )}

      <div className="comment-respond">
        <CommentForm
          postId={postId}
          onCommentAdded={handleCommentAdded}
        />
      </div>
    </div>
  );
};

export default CommentList;
