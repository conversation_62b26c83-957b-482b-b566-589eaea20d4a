"use client";

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { commentApi } from '@/api/blog/comment';
import { TComment } from '@/types/blog/comment';
import { formatDate } from '@/utils/helper';

interface CommentListProps {
  postId: number;
}

interface CommentItemProps {
  comment: TComment;
  level: number;
}

const CommentItem: React.FC<CommentItemProps> = ({
  comment,
  level
}) => {
  const getAvatarUrl = (email: string) => {
    // Sử dụng Gravatar hoặc avatar mặc định
    return comment.author_avatar || `https://www.gravatar.com/avatar/${btoa(email)}?d=identicon&s=95`;
  };

  return (
    <li className="comment">
      <article className="comment-body">
        <footer className="comment-meta">
          <div className="comment-author vcard">
            <Image
              src={getAvatarUrl(comment.author_email)}
              className="avatar"
              alt={comment.author_name}
              width={95}
              height={95}
            />
            <b className="fn">{comment.author_name}</b>
            <span className="says">says:</span>
          </div>

          <div className="comment-metadata">
            {formatDate(comment.created_at)}
          </div>
        </footer>

        <div className="comment-content">
          <p>{comment.content}</p>
        </div>
      </article>

      {/* Nested Replies */}
      {comment.replies && comment.replies.length > 0 && (
        <ol className="children">
          {comment.replies.map((reply) => (
            <CommentItem
              key={reply.id}
              comment={reply}
              level={level + 1}
            />
          ))}
        </ol>
      )}
    </li>
  );
};

const CommentList: React.FC<CommentListProps> = ({ postId }) => {
  const [comments, setComments] = useState<TComment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchComments = async () => {
    try {
      setLoading(true);
      const response = await commentApi.getByPost(postId);

      if (response.error) {
        setError(response.message || 'Failed to load comments');
      } else {
        setComments(response.data || []);
        setError(null);
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchComments();
  }, [postId]);

  if (loading) {
    return (
      <div className="comments-area">
        <div className="loading">Loading comments...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="comments-area">
        <div className="error">Error: {error}</div>
      </div>
    );
  }

  return (
    <div className="comments-area">
      <h3 id="comments" className="comments-title">
        {comments.length} {comments.length === 1 ? 'Comment' : 'Comments'}
      </h3>

      {comments.length > 0 && (
        <ol className="comment-list">
          {comments.map((comment) => (
            <CommentItem
              key={comment.id}
              comment={comment}
              level={0}
            />
          ))}
        </ol>
      )}
    </div>
  );
};

export default CommentList;
