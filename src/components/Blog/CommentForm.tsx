"use client";

import React, { useState } from 'react';
import { commentApi } from '@/api/blog/comment';
import { ICommentCreateRequest } from '@/types/blog/comment';

interface CommentFormProps {
  postId: number;
  parentId?: number;
  onCommentAdded?: () => void;
  onCancel?: () => void;
  isReply?: boolean;
  placeholder?: string;
}

const CommentForm: React.FC<CommentFormProps> = ({
  postId,
  parentId,
  onCommentAdded,
  onCancel,
  isReply = false,
  placeholder = "Write your comment..."
}) => {
  const [formData, setFormData] = useState({
    content: '',
    author_name: '',
    author_email: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      const commentData: ICommentCreateRequest = {
        ...formData,
        post_id: postId,
        ...(parentId && { parent_id: parentId })
      };

      let response;
      if (parentId) {
        response = await commentApi.reply(parentId, {
          content: formData.content,
          author_name: formData.author_name,
          author_email: formData.author_email,
          post_id: postId
        });
      } else {
        response = await commentApi.create(commentData);
      }

      if (response.error) {
        setError(response.message || 'Failed to submit comment');
      } else {
        setFormData({ content: '', author_name: '', author_email: '' });
        onCommentAdded?.();
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={`comment-form-wrapper ${isReply ? 'reply-form' : ''}`}>
      <h4>{isReply ? 'Leave a Reply' : 'Leave a Comment'}</h4>
      
      {error && (
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      )}

      <form className="comment-form" onSubmit={handleSubmit}>
        <p className="comment-notes">
          <span id="email-notes">
            Your email address will not be published.
          </span>
          Required fields are marked
          <span className="required">*</span>
        </p>

        <p className="comment-form-comment">
          <label>Comment <span className="required">*</span></label>
          <textarea
            name="content"
            id="comment"
            cols={45}
            rows={5}
            maxLength={65525}
            required
            value={formData.content}
            onChange={handleInputChange}
            placeholder={placeholder}
          />
        </p>

        <p className="comment-form-author">
          <label>
            Name <span className="required">*</span>
          </label>
          <input
            type="text"
            id="author"
            name="author_name"
            required
            value={formData.author_name}
            onChange={handleInputChange}
          />
        </p>

        <p className="comment-form-email">
          <label>
            Email <span className="required">*</span>
          </label>
          <input
            type="email"
            id="email"
            name="author_email"
            required
            value={formData.author_email}
            onChange={handleInputChange}
          />
        </p>

        <div className="form-submit">
          <input
            name="submit"
            type="submit"
            id="submit"
            className="submit"
            value={isSubmitting ? 'Submitting...' : 'Post Comment'}
            disabled={isSubmitting}
          />
          {isReply && onCancel && (
            <button
              type="button"
              className="cancel-reply"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </button>
          )}
        </div>
      </form>
    </div>
  );
};

export default CommentForm;
