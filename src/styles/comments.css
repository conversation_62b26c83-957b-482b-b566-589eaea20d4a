/* Comment System Styles */
.comments-area {
  margin-top: 50px;
}

.comment-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.comment {
  margin-bottom: 30px;
}

.comment-body {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 8px;
  position: relative;
}

.comment-meta {
  margin-bottom: 15px;
}

.comment-author {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 10px;
}

.comment-author .avatar {
  border-radius: 50%;
  flex-shrink: 0;
}

.comment-author .fn {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.comment-author .says {
  color: #666;
  font-size: 14px;
  margin-left: 5px;
}

.comment-metadata {
  color: #888;
  font-size: 13px;
}

.comment-content {
  margin: 15px 0;
  line-height: 1.6;
}

.comment-content p {
  margin: 0;
  color: #555;
}

.reply {
  margin-top: 15px;
}

.comment-reply-link {
  color: #007bff;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
}

.comment-reply-link:hover {
  color: #0056b3;
  text-decoration: underline;
}

/* Nested Comments */
.children {
  list-style: none;
  padding: 0;
  margin: 20px 0 0 30px;
}

.children .comment-body {
  background: #ffffff;
  border: 1px solid #e9ecef;
}

/* Reply Form */
.reply-form-container {
  margin: 20px 0 0 30px;
  padding: 20px;
  background: #f1f3f4;
  border-radius: 8px;
}

.reply-form .comment-form {
  margin: 0;
}

.reply-form h4 {
  margin-bottom: 20px;
  font-size: 18px;
  color: #333;
}

/* Comment Form */
.comment-form {
  margin-top: 30px;
}

.comment-form h4 {
  margin-bottom: 20px;
  font-size: 20px;
  color: #333;
}

.comment-notes {
  margin-bottom: 20px;
  font-size: 14px;
  color: #666;
}

.required {
  color: #dc3545;
}

.comment-form p {
  margin-bottom: 20px;
}

.comment-form label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.comment-form input,
.comment-form textarea {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.comment-form input:focus,
.comment-form textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.comment-form textarea {
  resize: vertical;
  min-height: 120px;
}

.form-submit {
  display: flex;
  gap: 10px;
  align-items: center;
}

.submit {
  background: #007bff;
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.submit:hover:not(:disabled) {
  background: #0056b3;
}

.submit:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.cancel-reply {
  background: #6c757d;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.cancel-reply:hover:not(:disabled) {
  background: #5a6268;
}

/* Loading and Error States */
.loading {
  text-align: center;
  padding: 40px;
  color: #666;
  font-style: italic;
}

.error {
  color: #dc3545;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
}

.alert {
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 5px;
}

.alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

/* Responsive */
@media (max-width: 768px) {
  .children {
    margin-left: 15px;
  }
  
  .reply-form-container {
    margin-left: 15px;
  }
  
  .comment-author {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .comment-author .avatar {
    width: 60px;
    height: 60px;
  }
}
