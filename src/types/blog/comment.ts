export interface TComment {
  id: number;
  content: string;
  author_name: string;
  author_email: string;
  author_avatar?: string;
  post_id: number;
  parent_id?: number;
  created_at: string;
  updated_at: string;
  replies?: TComment[];
  replies_count?: number;
}

export interface ICommentResponse {
  data: TComment[];
  meta?: {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    total: number;
  };
  error?: boolean;
  message?: string;
}

export interface ICommentCreateRequest {
  content: string;
  author_name: string;
  author_email: string;
  post_id: number;
  parent_id?: number;
}

export interface ICommentCreateResponse {
  data: TComment;
  error?: boolean;
  message?: string;
}

export type CommentParams = {
  page?: string | number;
  per_page?: string | number;
  post_id?: string | number;
  parent_id?: string | number;
};
