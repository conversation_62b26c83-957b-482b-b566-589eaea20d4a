import { fetcher } from '@/api';
import { BLOG_ENDPOINT } from '@/constants/endpoint';
import { 
  ICommentResponse, 
  ICommentCreateRequest, 
  ICommentCreateResponse, 
  CommentParams 
} from '@/types/blog/comment';

export const commentApi = {
  // Get comments by post ID
  getByPost: async (
    postId: string | number,
    params?: CommentParams
  ): Promise<ICommentResponse> => {
    try {
      const response = await fetcher<ICommentResponse>({
        endpoint: BLOG_ENDPOINT.GET_COMMENTS_BY_POST(postId),
        params,
        type: 'blog'
      });
      return {
        data: response.data?.data || [],
        meta: response.meta,
        error: response.error ? true : false,
        message: response.error?.message
      };
    } catch (error) {
      return {
        data: [],
        error: true,
        message: 'Failed to fetch comments'
      };
    }
  },

  // Create new comment
  create: async (
    commentData: ICommentCreateRequest
  ): Promise<ICommentCreateResponse> => {
    try {
      const response = await fetcher<ICommentCreateResponse>({
        endpoint: BLOG_ENDPOINT.CREATE_COMMENT,
        method: 'POST',
        body: JSON.stringify(commentData),
        type: 'blog'
      });
      return {
        data: response.data?.data,
        error: response.error ? true : false,
        message: response.error?.message || 'Comment created successfully'
      };
    } catch (error) {
      return {
        data: null,
        error: true,
        message: 'Failed to create comment'
      };
    }
  },

  // Reply to a comment
  reply: async (
    parentCommentId: string | number,
    commentData: Omit<ICommentCreateRequest, 'parent_id'>
  ): Promise<ICommentCreateResponse> => {
    try {
      const response = await fetcher<ICommentCreateResponse>({
        endpoint: BLOG_ENDPOINT.REPLY_COMMENT(parentCommentId),
        method: 'POST',
        body: JSON.stringify({
          ...commentData,
          parent_id: parentCommentId
        }),
        type: 'blog'
      });
      return {
        data: response.data?.data,
        error: response.error ? true : false,
        message: response.error?.message || 'Reply created successfully'
      };
    } catch (error) {
      return {
        data: null,
        error: true,
        message: 'Failed to create reply'
      };
    }
  }
};
