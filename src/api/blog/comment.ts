import { fetcher } from '@/api';
import { BLOG_ENDPOINT } from '@/constants/endpoint';
import {
  ICommentResponse,
  CommentParams
} from '@/types/blog/comment';

export const commentApi = {
  // Get comments by post ID
  getByPost: async (
    postId: string | number,
    params?: CommentParams
  ): Promise<ICommentResponse> => {
    try {
      const response = await fetcher<ICommentResponse>({
        endpoint: BLOG_ENDPOINT.GET_COMMENTS_BY_POST(postId),
        params,
        type: 'blog'
      });
      return {
        data: response.data?.data || [],
        meta: response.meta,
        error: response.error ? true : false,
        message: response.error?.message
      };
    } catch (error) {
      return {
        data: [],
        error: true,
        message: 'Failed to fetch comments'
      };
    }
  }
};
